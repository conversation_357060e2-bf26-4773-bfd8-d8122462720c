"""
Stage 2: Website Configuration

This module handles website URL configuration and API key management.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import os
import json
import logging
import streamlit as st

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage2")

# Import configuration
from core.config import APP_CONFIG_FILE

def load_google_api_key():
    """Load Google API key from config file or environment variable."""
    # Try to load from config file first
    try:
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, "r") as f:
                config = json.load(f)
                if "google_api_key" in config and config["google_api_key"]:
                    return config["google_api_key"]
    except Exception as e:
        logger.error(f"Error loading config file: {e}")

    # Fall back to environment variable
    return os.environ.get("GOOGLE_API_KEY", "")

def stage2_enter_website(state):
    """Phase 2: Website Configuration."""
    st.markdown("<h2 class='stage-header'>Phase 2: Website Configuration</h2>", unsafe_allow_html=True)

    # Main configuration section
    with st.container():
        # Get existing URL from state or use default
        default_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
        website_url = st.text_input("Website URL", value=default_url)

        # Store the URL in state manager
        if state.website_url != website_url:
            state.website_url = website_url
            logger.info(f"State change: website_url = {website_url}")

            # Advance to Stage 3 if a valid URL is entered
            if website_url and website_url != "https://example.com":
                from state_manager import StageEnum
                state.advance_to_stage(StageEnum.STAGE_3, f"Website URL configured: {website_url}")

    # API Key section in collapsible expander
    with st.expander("API Configuration", expanded=False):
        # Try to get the API key from config or environment
        default_api_key = load_google_api_key()

        # Store the API key in state manager for later use
        if not hasattr(state, 'google_api_key') or not state.google_api_key:
            state.google_api_key = default_api_key

        # Only show API key input if no key was found
        if not default_api_key:
            api_key_col1, api_key_col2 = st.columns([3, 1])
            with api_key_col1:
                google_api_key = st.text_input(
                    "Google API Key",
                    type="password",
                    help="Required for AI features"
                )
                state.google_api_key = google_api_key

            with api_key_col2:
                if st.button("Save Key", help="Save to config.json"):
                    if google_api_key:
                        try:
                            config = {"google_api_key": google_api_key}
                            with open(APP_CONFIG_FILE, "w") as f:
                                json.dump(config, f, indent=2)
                            st.success("✅ API key saved")

                            # Also set as environment variable for this session
                            os.environ["GOOGLE_API_KEY"] = google_api_key
                        except Exception as e:
                            st.error(f"❌ Error: {e}")
                    else:
                        st.warning("⚠️ Enter an API key")
        else:
            # Just show a message that an API key was found
            st.success("✅ Google API key configured")
            # Store the API key in state manager
            state.google_api_key = default_api_key

    # Advanced options in collapsible expander
    with st.expander("Advanced Options", expanded=False):
        st.markdown("##### Element Detection Settings")

        # Options for element detection and matching
        col1, col2 = st.columns(2)
        with col1:
            # Note: This variable is used implicitly by Streamlit's state management
            auto_detect = st.checkbox("Auto-detect UI elements", value=True, key="auto_detect",
                                help="Automatically detect elements from website")
            # Store in state manager for consistency
            state.auto_detect = auto_detect

        with col2:
            # Get default value from state if available
            default_ai_matching = state.use_ai_matching if hasattr(state, 'use_ai_matching') else True
            use_ai_matching = st.checkbox("AI element matching", value=default_ai_matching,
                                        help="Use AI to match test steps to UI elements")
            # Store in state manager for use in other stages
            if not hasattr(state, 'use_ai_matching') or state.use_ai_matching != use_ai_matching:
                state.use_ai_matching = use_ai_matching
                logger.info(f"State change: use_ai_matching = {use_ai_matching}")
